import Razorpay from 'razorpay';
import crypto from 'crypto';
import { validationResult } from 'express-validator';
import CreditService from '../services/creditService.js';
import CreditTransaction, { TRANSACTION_STATUS } from '../models/CreditTransaction.js';
import Student from '../models/Student.js';
import Teacher from '../models/Teacher.js';
import auditLogger from '../utils/auditLogger.js';
import distributedLock from '../utils/distributedLock.js';

// Initialize Razorpay (only if credentials are provided)
let razorpay = null;
if (process.env.RAZORPAY_KEY_ID && process.env.RAZORPAY_KEY_SECRET) {
    razorpay = new Razorpay({
        key_id: process.env.RAZORPAY_KEY_ID,
        key_secret: process.env.RAZORPAY_KEY_SECRET,
    });
    console.log('Razorpay initialized successfully');
} else {
    console.warn('Razorpay credentials not found. Credit purchase functionality will be disabled.');
}

// Credit packages configuration
const CREDIT_PACKAGES = {
    BASIC_10: {
        credits: 10,
        amount: 249900, // Amount in paisa (₹2499.00)
        name: 'Newbie Package',
        description: 'Basic package - 10 AegisGrader evaluations',
        pricePerCredit: 24990 // ₹249.90 per credit in paisa
    },
    STANDARD_50: {
        credits: 50,
        amount: 1125000, // Amount in paisa (₹11250.00) - 10% discount
        name: 'Novice Package',
        description: 'Standard package - 50 AegisGrader evaluations',
        pricePerCredit: 22500 // ₹225.00 per credit in paisa
    },
    PREMIUM_100: {
        credits: 100,
        amount: 2000000, // Amount in paisa (₹20000.00) - 20% discount
        name: 'Pro Package',
        description: 'Premium package - 100 AegisGrader evaluations',
        pricePerCredit: 20000 // ₹200.00 per credit in paisa
    }
};

/**
 * Get available credit packages
 */
export const getCreditPackages = async (req, res) => {
    try {
        const packages = Object.entries(CREDIT_PACKAGES).map(([key, pkg]) => ({
            id: key,
            ...pkg,
            amountInRupees: pkg.amount / 100
        }));

        res.json({
            success: true,
            data: packages
        });
    } catch (error) {
        console.error('Error getting credit packages:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching credit packages'
        });
    }
};

/**
 * Get user's credit balance and statistics
 */
export const getCreditBalance = async (req, res) => {
    try {
        const userId = req.id;
        
        // Determine user type
        let userType = null;
        let user = await Student.findById(userId).select('role');
        if (user) {
            userType = 'Student';
        } else {
            user = await Teacher.findById(userId).select('role');
            if (user) {
                userType = 'Teacher';
            }
        }

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        const stats = await CreditService.getCreditStats(userId, userType);
        
        res.json({
            success: true,
            data: stats
        });
    } catch (error) {
        console.error('Error getting credit balance:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching credit balance'
        });
    }
};

/**
 * Create Razorpay order for credit purchase
 */
export const createCreditOrder = async (req, res) => {
    try {
        // Check if Razorpay is initialized
        if (!razorpay) {
            return res.status(503).json({
                success: false,
                message: 'Payment service is not configured. Please contact support.',
                code: 'PAYMENT_SERVICE_UNAVAILABLE'
            });
        }

        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                errors: errors.array()
            });
        }

        const { packageId, customPackage } = req.body;
        const userId = req.id;

        let packageInfo;

        // Handle custom packages for new pricing structure
        if (customPackage) {
            packageInfo = customPackage;
        } else {
            // Validate existing package
            packageInfo = CREDIT_PACKAGES[packageId];
            if (!packageInfo) {
                return res.status(400).json({
                    success: false,
                    message: 'Invalid package selected'
                });
            }
        }

        // Determine user type
        let userType = null;
        let user = await Student.findById(userId).select('role email username');
        if (user) {
            userType = 'Student';
        } else {
            user = await Teacher.findById(userId).select('role email username');
            if (user) {
                userType = 'Teacher';
            }
        }

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Create Razorpay order
        const orderOptions = {
            amount: packageInfo.amount,
            currency: 'INR',
            receipt: `cr_${userId.slice(-8)}_${Date.now().toString().slice(-8)}`, // Keep under 40 chars
            notes: {
                userId: userId,
                userType: userType,
                packageId: packageId,
                credits: packageInfo.credits.toString()
            }
        };

        const razorpayOrder = await razorpay.orders.create(orderOptions);

        // Create pending transaction in database
        const transaction = await CreditService.createPendingTransaction(userId, userType, packageInfo.credits, {
            razorpayOrderId: razorpayOrder.id,
            amount: packageInfo.amount,
            currency: 'INR',
            packageType: packageId,
            packageName: packageInfo.name
        });

        res.json({
            success: true,
            data: {
                orderId: razorpayOrder.id,
                amount: razorpayOrder.amount,
                currency: razorpayOrder.currency,
                packageInfo: {
                    ...packageInfo,
                    id: packageId
                },
                transactionId: transaction.transactionId
            }
        });

    } catch (error) {
        console.error('Error creating credit order:', error);
        res.status(500).json({
            success: false,
            message: 'Error creating payment order'
        });
    }
};

/**
 * Verify Razorpay payment and add credits
 */
export const verifyPayment = async (req, res) => {
    try {
        const { razorpay_order_id } = req.body;

        // RACE CONDITION PREVENTION: Use distributed lock
        return await distributedLock.withLock(razorpay_order_id, async () => {
            return await processPaymentVerification(req, res);
        });
    } catch (error) {
        if (error.message.includes('Unable to acquire lock')) {
            return res.status(409).json({
                success: false,
                message: 'Payment verification already in progress'
            });
        }
        throw error;
    }
};

/**
 * Internal payment verification logic
 */
const processPaymentVerification = async (req, res) => {
    let transaction = null;
    let session = null;

    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                errors: errors.array()
            });
        }

        const { razorpay_order_id, razorpay_payment_id, razorpay_signature } = req.body;
        const userId = req.id;

        // Input validation
        if (!razorpay_order_id || !razorpay_payment_id || !razorpay_signature) {
            return res.status(400).json({
                success: false,
                message: 'Missing required payment parameters'
            });
        }

        // Verify signature
        const body = razorpay_order_id + "|" + razorpay_payment_id;
        const expectedSignature = crypto
            .createHmac("sha256", process.env.RAZORPAY_KEY_SECRET)
            .update(body.toString())
            .digest("hex");

        if (expectedSignature !== razorpay_signature) {
            console.warn(`Invalid payment signature for order ${razorpay_order_id} by user ${userId}`);
            auditLogger.logSecurityViolation('INVALID_PAYMENT_SIGNATURE', {
                userId,
                orderId: razorpay_order_id,
                paymentId: razorpay_payment_id
            });
            return res.status(400).json({
                success: false,
                message: 'Invalid payment signature'
            });
        }

        // Start MongoDB session for atomic operations
        session = await CreditTransaction.startSession();
        session.startTransaction();

        // Find and lock the pending transaction (with user validation)
        transaction = await CreditTransaction.findOne({
            'payment.razorpayOrderId': razorpay_order_id,
            userId: userId, // SECURITY: Ensure transaction belongs to authenticated user
            status: TRANSACTION_STATUS.PENDING
        }).session(session);

        if (!transaction) {
            await session.abortTransaction();
            auditLogger.logSecurityViolation('UNAUTHORIZED_PAYMENT_ACCESS', {
                userId,
                orderId: razorpay_order_id,
                paymentId: razorpay_payment_id
            });
            return res.status(404).json({
                success: false,
                message: 'Transaction not found or unauthorized'
            });
        }

        // SECURITY: Validate transaction hasn't been processed already
        if (transaction.payment.razorpayPaymentId) {
            await session.abortTransaction();
            return res.status(409).json({
                success: false,
                message: 'Transaction already processed'
            });
        }

        // SECURITY: Validate payment amount matches original package price
        const packageInfo = CREDIT_PACKAGES[transaction.payment.packageType];
        if (!packageInfo || packageInfo.amount !== transaction.payment.amount) {
            await session.abortTransaction();
            console.error(`Amount mismatch for transaction ${transaction.transactionId}: expected ${packageInfo?.amount}, got ${transaction.payment.amount}`);
            return res.status(400).json({
                success: false,
                message: 'Payment amount validation failed'
            });
        }

        // Update transaction with payment details
        transaction.payment.razorpayPaymentId = razorpay_payment_id;
        transaction.payment.razorpaySignature = razorpay_signature;
        transaction.status = TRANSACTION_STATUS.COMPLETED;
        transaction.completedAt = new Date();
        await transaction.save({ session });

        // ATOMIC: Add credits and update billing in single operation
        const UserModel = transaction.userType === 'Student' ? Student : Teacher;
        const user = await UserModel.findById(transaction.userId).select('credits billing').session(session);

        if (!user) {
            await session.abortTransaction();
            throw new Error('User not found');
        }

        const currentBalance = user.credits?.balance || 0;
        const newBalance = currentBalance + transaction.creditAmount;
        const totalEarned = (user.credits?.totalEarned || 0) + transaction.creditAmount;

        // ATOMIC: Update credits and billing in single operation
        await UserModel.findByIdAndUpdate(
            transaction.userId,
            {
                $set: {
                    'credits.balance': newBalance,
                    'credits.totalEarned': totalEarned,
                    'credits.lastUpdated': new Date(),
                    'billing.lastPurchaseDate': new Date()
                },
                $inc: {
                    'billing.totalAmountSpent': transaction.payment.amount
                }
            },
            { session }
        );

        // Commit the transaction
        await session.commitTransaction();

        // Log successful payment verification
        auditLogger.logPaymentVerificationAttempt(userId, razorpay_order_id, razorpay_payment_id, true, 'Payment verified successfully');
        auditLogger.logCreditBalanceChange(userId, transaction.userType, currentBalance, newBalance, 'Credit purchase', transaction.transactionId);

        res.json({
            success: true,
            message: 'Payment verified and credits added successfully',
            data: {
                creditsAdded: transaction.creditAmount,
                newBalance: newBalance,
                transactionId: transaction.transactionId
            }
        });

    } catch (error) {
        console.error('Error verifying payment:', error);

        // Rollback transaction if it exists
        if (session) {
            try {
                await session.abortTransaction();
            } catch (rollbackError) {
                console.error('Error rolling back transaction:', rollbackError);
            }
        }

        // Mark transaction as failed if it exists and wasn't already processed
        if (transaction && transaction.status === TRANSACTION_STATUS.PENDING) {
            try {
                await transaction.markFailed(`Payment verification failed: ${error.message}`);
            } catch (markFailedError) {
                console.error('Error marking transaction as failed:', markFailedError);
            }
        }

        // Determine appropriate error response
        let statusCode = 500;
        let message = 'Error verifying payment';

        if (error.message.includes('User not found')) {
            statusCode = 404;
            message = 'User account not found';
        } else if (error.message.includes('validation failed')) {
            statusCode = 400;
            message = 'Payment validation failed';
        }

        res.status(statusCode).json({
            success: false,
            message: message,
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    } finally {
        // Ensure session is always ended
        if (session) {
            try {
                await session.endSession();
            } catch (sessionError) {
                console.error('Error ending session:', sessionError);
            }
        }
    }
};

// Webhook idempotency cache (in production, use Redis)
const webhookCache = new Map();
const WEBHOOK_CACHE_TTL = 5 * 60 * 1000; // 5 minutes

/**
 * Handle Razorpay webhooks with security and idempotency
 */
export const handleWebhook = async (req, res) => {
    try {
        // Check if Razorpay is configured
        if (!razorpay) {
            console.error('Razorpay not configured, ignoring webhook');
            return res.status(503).json({ error: 'Payment service not configured' });
        }

        const webhookSignature = req.headers['x-razorpay-signature'];
        const webhookSecret = process.env.RAZORPAY_WEBHOOK_SECRET;

        if (!webhookSecret) {
            console.error('Razorpay webhook secret not configured');
            return res.status(500).json({ error: 'Webhook secret not configured' });
        }

        // SECURITY: Verify webhook signature
        const rawBody = JSON.stringify(req.body);
        const expectedSignature = crypto
            .createHmac('sha256', webhookSecret)
            .update(rawBody)
            .digest('hex');

        if (expectedSignature !== webhookSignature) {
            console.error('Invalid webhook signature');
            return res.status(400).json({ error: 'Invalid signature' });
        }

        const { event, payload } = req.body;

        // IDEMPOTENCY: Check if webhook already processed
        const webhookId = req.headers['x-razorpay-event-id'] || `${event}_${payload?.payment?.entity?.id || payload?.order?.entity?.id}_${Date.now()}`;

        if (webhookCache.has(webhookId)) {
            console.log(`Webhook ${webhookId} already processed, ignoring`);
            return res.json({ status: 'ok', message: 'Already processed' });
        }

        // Add to cache with TTL
        webhookCache.set(webhookId, true);
        setTimeout(() => webhookCache.delete(webhookId), WEBHOOK_CACHE_TTL);

        // Process webhook events
        switch (event) {
            case 'payment.failed':
                await handlePaymentFailed(payload.payment.entity);
                break;
            case 'payment.captured':
                // Payment was successful - this is handled by our verify endpoint
                console.log('Payment captured:', payload.payment.entity.id);
                break;
            case 'order.paid':
                console.log('Order paid:', payload.order.entity.id);
                break;
            default:
                console.log('Unhandled webhook event:', event);
        }

        res.json({ status: 'ok' });
    } catch (error) {
        console.error('Webhook error:', error);
        res.status(500).json({ error: 'Webhook processing failed' });
    }
};

/**
 * Handle failed payment webhook
 */
const handlePaymentFailed = async (payment) => {
    try {
        const transaction = await CreditTransaction.findOne({
            'payment.razorpayOrderId': payment.order_id,
            status: TRANSACTION_STATUS.PENDING
        });

        if (transaction) {
            await transaction.markFailed(`Payment failed: ${payment.error_description || 'Unknown error'}`);
            console.log(`Marked transaction ${transaction.transactionId} as failed due to payment failure`);
        }
    } catch (error) {
        console.error('Error handling payment failure:', error);
    }
};

/**
 * Get user's transaction history with enhanced filtering
 */
export const getTransactionHistory = async (req, res) => {
    try {
        const userId = req.id;
        const {
            page = 1,
            limit = 20,
            type,
            types, // Support multiple types as comma-separated string
            status,
            dateFrom,
            dateTo,
            feature
        } = req.query;

        // Determine user type
        let userType = null;
        let user = await Student.findById(userId).select('role');
        if (user) {
            userType = 'Student';
        } else {
            user = await Teacher.findById(userId).select('role');
            if (user) {
                userType = 'Teacher';
            }
        }

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        const options = {
            limit: parseInt(limit),
            skip: (parseInt(page) - 1) * parseInt(limit)
        };

        if (type) options.type = type;
        if (types) {
            // Support multiple types as comma-separated string
            options.types = types.split(',').map(t => t.trim());
        }
        if (status) options.status = status;
        if (dateFrom) options.dateFrom = dateFrom;
        if (dateTo) options.dateTo = dateTo;
        if (feature) options.feature = feature;

        const result = await CreditService.getTransactionHistory(userId, userType, options);

        res.json({
            success: true,
            data: {
                transactions: result.transactions,
                pagination: {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    total: result.totalCount,
                    totalPages: result.totalPages
                }
            }
        });

    } catch (error) {
        console.error('Error getting transaction history:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching transaction history'
        });
    }
};

/**
 * Get billing analytics for user
 */
export const getBillingAnalytics = async (req, res) => {
    try {
        const userId = req.id;
        const { timeRange = 'month' } = req.query;

        // Determine user type
        let userType = null;
        let user = await Student.findById(userId).select('role');
        if (user) {
            userType = 'Student';
        } else {
            user = await Teacher.findById(userId).select('role');
            if (user) {
                userType = 'Teacher';
            }
        }

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        const [usageAnalytics, paymentAnalytics] = await Promise.all([
            CreditService.getUsageAnalytics(userId, userType, timeRange),
            CreditService.getPaymentAnalytics(userId, userType, timeRange)
        ]);

        res.json({
            success: true,
            data: {
                usageAnalytics,
                paymentAnalytics
            }
        });

    } catch (error) {
        console.error('Error getting billing analytics:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching billing analytics'
        });
    }
};

/**
 * Get comprehensive billing dashboard data
 */
export const getBillingDashboard = async (req, res) => {
    try {
        const userId = req.id;

        // Determine user type
        let userType = null;
        let user = await Student.findById(userId).select('credits billing');
        if (user) {
            userType = 'Student';
        } else {
            user = await Teacher.findById(userId).select('credits billing');
            if (user) {
                userType = 'Teacher';
            }
        }

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Get all data in parallel
        const [
            creditStats,
            recentTransactionsResult,
            usageAnalytics,
            paymentAnalytics
        ] = await Promise.all([
            CreditService.getCreditStats(userId, userType),
            CreditService.getTransactionHistory(userId, userType, { limit: 10 }),
            CreditService.getUsageAnalytics(userId, userType, 'month'),
            CreditService.getPaymentAnalytics(userId, userType, 'year')
        ]);

        res.json({
            success: true,
            data: {
                creditBalance: {
                    currentBalance: creditStats.currentBalance,
                    totalEarned: creditStats.totalEarned,
                    totalSpent: creditStats.totalSpent,
                    totalPurchased: creditStats.totalPurchased,
                    lastUpdated: user.credits?.lastUpdated || new Date()
                },
                billingInfo: {
                    customerId: user.billing?.customerId,
                    lastPurchaseDate: user.billing?.lastPurchaseDate,
                    totalAmountSpent: user.billing?.totalAmountSpent || 0
                },
                recentTransactions: recentTransactionsResult.transactions,
                usageAnalytics,
                paymentAnalytics,
                totalTransactions: recentTransactionsResult.totalCount
            }
        });

    } catch (error) {
        console.error('Error getting billing dashboard:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching billing dashboard data'
        });
    }
};
