import React, { useState } from 'react';
import { XMarkIcon, CreditCardIcon, ArrowPathIcon, SparklesIcon } from '@heroicons/react/24/outline';
import { useAxiosPrivate } from '@/hooks/useAxiosPrivate';
import { toast } from 'react-toastify';
import { createPortal } from 'react-dom';

interface PricingTier {
    name: string;
    minCredits: number;
    maxCredits: number | null;
    pricePerCredit: number;
    discount: number;
    description: string;
    badge?: string;
    badgeColor?: string;
}

interface CreditPurchaseModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSuccess?: (creditsAdded: number, newBalance: number) => void;
}

declare global {
    interface Window {
        Razorpay: any;
    }
}

// Pricing tiers based on the new structure
const PRICING_TIERS: PricingTier[] = [
    {
        name: 'Professional',
        minCredits: 500,
        maxCredits: 5000,
        pricePerCredit: 200,
        discount: 0,
        description: 'Perfect for individual educators and small teams',
        badge: 'Most Popular',
        badgeColor: 'bg-blue-500'
    },
    {
        name: 'Premium',
        minCredits: 5001,
        maxCredits: 10000,
        pricePerCredit: 175,
        discount: 25,
        description: 'Great for departments and larger institutions',
        badge: 'Best Value',
        badgeColor: 'bg-green-500'
    },
    {
        name: 'Enterprise',
        minCredits: 10001,
        maxCredits: null,
        pricePerCredit: 0, // Will be negotiable
        discount: 0,
        description: 'Custom solutions for large organizations',
        badge: 'Contact Us',
        badgeColor: 'bg-purple-500'
    }
];

const CreditPurchaseModal: React.FC<CreditPurchaseModalProps> = ({
    isOpen,
    onClose,
    onSuccess
}) => {
    const [selectedCredits, setSelectedCredits] = useState(500);
    const [customAmount, setCustomAmount] = useState('500');
    const [purchasing, setPurchasing] = useState(false);
    const axiosPrivate = useAxiosPrivate();

    // Calculate pricing based on selected credits
    const getCurrentTier = (credits: number): PricingTier => {
        if (credits >= 10001) return PRICING_TIERS[2];
        if (credits >= 5001) return PRICING_TIERS[1];
        return PRICING_TIERS[0];
    };

    const calculatePrice = (credits: number) => {
        const tier = getCurrentTier(credits);
        if (tier.name === 'Enterprise') {
            return { total: 0, perCredit: 0, savings: 0 };
        }

        const total = credits * tier.pricePerCredit;
        const originalPrice = credits * 150; // Base price
        const savings = originalPrice - total;

        return {
            total,
            perCredit: tier.pricePerCredit,
            savings
        };
    };

    const currentTier = getCurrentTier(selectedCredits);
    const pricing = calculatePrice(selectedCredits);

    const handleCreditChange = (value: string) => {
        // Only allow integers (remove any non-digit characters)
        const cleanValue = value.replace(/[^0-9]/g, '');
        const numValue = parseInt(cleanValue) || 0;
        setCustomAmount(cleanValue);
        if (numValue >= 500) {
            setSelectedCredits(numValue);
        }
    };

    const handleQuickSelect = (credits: number) => {
        setSelectedCredits(credits);
        setCustomAmount(credits.toString());
    };

    const handlePurchase = async () => {
        if (selectedCredits < 500) {
            toast.error('Minimum purchase is 500 credits');
            return;
        }

        if (currentTier.name === 'Enterprise') {
            toast.info('Please contact us for Enterprise pricing');
            return;
        }

        try {
            setPurchasing(true);

            // Create a custom package for the API
            const customPackage = {
                credits: selectedCredits,
                amount: pricing.total * 100, // Convert to paisa
                name: `${selectedCredits} Credits - ${currentTier.name}`,
                description: `${selectedCredits} AegisGrader evaluations`,
                pricePerCredit: pricing.perCredit * 100 // Convert to paisa
            };

            // For now, we'll use a custom package ID
            const packageId = `CUSTOM_${selectedCredits}`;

            // Create Razorpay order
            const orderResponse = await axiosPrivate.post('/api/credits/order', {
                packageId: packageId,
                customPackage: customPackage
            });

            const { orderId, amount, currency, packageInfo } = orderResponse.data.data;

            // Initialize Razorpay payment
            const options = {
                key: import.meta.env.VITE_RAZORPAY_KEY_ID,
                amount: amount,
                currency: currency,
                name: 'AegisScholar',
                description: `Purchase ${packageInfo.name}`,
                order_id: orderId,
                handler: async (response: any) => {
                    try {
                        // Verify payment
                        const verifyResponse = await axiosPrivate.post('/api/credits/verify', {
                            razorpay_order_id: response.razorpay_order_id,
                            razorpay_payment_id: response.razorpay_payment_id,
                            razorpay_signature: response.razorpay_signature
                        });

                        const { creditsAdded, newBalance } = verifyResponse.data.data;
                        
                        toast.success(`Successfully purchased ${creditsAdded} credits!`, {
                            autoClose: 5000
                        });
                        
                        if (onSuccess) {
                            onSuccess(creditsAdded, newBalance);
                        }
                        
                        onClose();
                    } catch (error: any) {
                        console.error('Payment verification failed:', error);
                        toast.error(error.response?.data?.message || 'Payment verification failed');
                    }
                },
                prefill: {
                    name: 'AegisScholar User',
                    email: '',
                    contact: ''
                },
                theme: {
                    color: '#3B82F6'
                },
                modal: {
                    ondismiss: () => {
                        setPurchasing(false);
                    }
                }
            };

            const razorpay = new window.Razorpay(options);
            razorpay.open();

        } catch (error: any) {
            console.error('Error creating order:', error);
            toast.error(error.response?.data?.message || 'Failed to create payment order');
            setPurchasing(false);
        }
    };

    if (!isOpen) return null;

    return createPortal(
        <div className="fixed inset-0 backdrop-blur-sm bg-black/20 flex items-center justify-center z-50 p-4">
            <div className="bg-card border border-border rounded-xl shadow-2xl max-w-4xl w-full max-h-[85dvh] overflow-auto">
                {/* Header */}
                <div className="flex items-center justify-between p-6 border-b border-border">
                    <div className="flex items-center gap-3">
                        <div className="p-2 bg-primary/10 rounded-xl">
                            <CreditCardIcon className="w-6 h-6 text-primary" />
                        </div>
                        <div>
                            <h2 className="text-xl font-semibold text-foreground">Purchase Credits</h2>
                            <p className="text-sm text-muted-foreground">Select the number of credits you need with tiered pricing</p>
                        </div>
                    </div>
                    <button
                        onClick={onClose}
                        className="p-2 hover:bg-muted rounded-xl transition-colors"
                        disabled={purchasing}
                    >
                        <XMarkIcon className="w-5 h-5 text-muted-foreground" />
                    </button>
                </div>

                {/* Content */}
                <div className="p-6">
                    <div className="grid md:grid-cols-2 gap-6">
                        {/* Left Column - Plan Selection */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-semibold text-foreground">Choose Your Plan</h3>
                            <div className="grid gap-3">
                                {PRICING_TIERS.map((tier) => (
                                    <div
                                        key={tier.name}
                                        className={`border rounded-xl p-4 cursor-pointer transition-all ${
                                            currentTier.name === tier.name
                                                ? 'border-primary bg-primary/5 ring-1 ring-primary/20'
                                                : 'border-border hover:border-primary/50'
                                        }`}
                                        onClick={() => {
                                            if (tier.name === 'Enterprise') {
                                                // For Enterprise, set a high number to trigger Enterprise tier
                                                handleQuickSelect(15000);
                                            } else {
                                                handleQuickSelect(tier.minCredits);
                                            }
                                        }}
                                    >
                                        <div className="flex items-center justify-between mb-2">
                                            <div className="flex items-center gap-2">
                                                <h4 className="font-semibold text-foreground">{tier.name}</h4>
                                                {tier.badge && (
                                                    <span className={`px-2 py-1 text-xs rounded-full text-white ${tier.badgeColor}`}>
                                                        {tier.badge}
                                                    </span>
                                                )}
                                            </div>
                                            {tier.discount > 0 && (
                                                <span className="text-green-600 dark:text-green-400 text-sm font-medium">
                                                    {tier.discount}% OFF
                                                </span>
                                            )}
                                        </div>
                                        <p className="text-sm text-muted-foreground mb-3">{tier.description}</p>
                                        <div className="flex items-center justify-between">
                                            <div className="text-sm">
                                                <span className="text-muted-foreground">
                                                    {tier.minCredits.toLocaleString()} - {tier.maxCredits ? tier.maxCredits.toLocaleString() : '∞'} credits
                                                </span>
                                            </div>
                                            <div className="text-right">
                                                {tier.name === 'Enterprise' ? (
                                                    <span className="text-sm font-medium text-foreground">Contact Us</span>
                                                ) : (
                                                    <span className="text-lg font-bold text-foreground">₹{tier.pricePerCredit}</span>
                                                )}
                                                {tier.name !== 'Enterprise' && (
                                                    <span className="text-xs text-muted-foreground block">per credit</span>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Right Column - Credit Input and Summary */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-semibold text-foreground">Select Credits</h3>

                            {/* Quick Select Buttons */}
                            <div className="space-y-2">
                                <div className="text-sm font-medium text-muted-foreground">Popular amounts:</div>
                                <div className="grid grid-cols-3 gap-2">
                                    {[500, 1000, 2500].map((amount) => (
                                        <button
                                            key={amount}
                                            onClick={() => handleQuickSelect(amount)}
                                            className={`px-3 py-2 rounded-lg text-sm font-medium transition-all ${
                                                selectedCredits === amount
                                                    ? 'bg-primary text-primary-foreground'
                                                    : 'bg-muted hover:bg-muted/80 text-foreground'
                                            }`}
                                        >
                                            {amount.toLocaleString()}
                                        </button>
                                    ))}
                                </div>
                                <div className="grid grid-cols-2 gap-2">
                                    {[5000, 10000].map((amount) => (
                                        <button
                                            key={amount}
                                            onClick={() => handleQuickSelect(amount)}
                                            className={`px-3 py-2 rounded-lg text-sm font-medium transition-all ${
                                                selectedCredits === amount
                                                    ? 'bg-primary text-primary-foreground'
                                                    : 'bg-muted hover:bg-muted/80 text-foreground'
                                            }`}
                                        >
                                            {amount.toLocaleString()}
                                        </button>
                                    ))}
                                </div>
                            </div>

                            {/* Custom Input */}
                            <div className="space-y-2">
                                <label className="text-sm font-medium text-foreground">Custom Amount</label>
                                <div className="relative">
                                    <input
                                        type="text"
                                        inputMode="numeric"
                                        pattern="[0-9]*"
                                        value={customAmount}
                                        onChange={(e) => handleCreditChange(e.target.value)}
                                        className="w-full px-4 py-3 pr-16 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                                        placeholder="Enter credits (min 500)"
                                    />
                                    <div className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                                        credits
                                    </div>
                                </div>
                                {selectedCredits < 500 && (
                                    <p className="text-sm text-red-500">Minimum purchase is 500 credits</p>
                                )}
                            </div>

                            {/* Order Summary */}
                            {selectedCredits >= 500 && currentTier.name !== 'Enterprise' && (
                                <div className="p-4 bg-muted/50 rounded-lg space-y-3">
                                    <h4 className="font-medium text-foreground flex items-center gap-2">
                                        <SparklesIcon className="w-4 h-4" />
                                        Order Summary
                                    </h4>
                                    <div className="space-y-2 text-sm">
                                        <div className="flex justify-between">
                                            <span className="text-muted-foreground">Plan:</span>
                                            <span className="text-foreground font-medium">{currentTier.name}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-muted-foreground">Credits:</span>
                                            <span className="text-foreground">{selectedCredits.toLocaleString()}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-muted-foreground">Price per credit:</span>
                                            <span className="text-foreground">₹{pricing.perCredit}</span>
                                        </div>
                                        {pricing.savings > 0 && (
                                            <div className="flex justify-between text-green-600 dark:text-green-400">
                                                <span>You save:</span>
                                                <span className="font-medium">₹{pricing.savings.toLocaleString()}</span>
                                            </div>
                                        )}
                                        <div className="flex justify-between text-lg font-bold pt-2 border-t border-border">
                                            <span className="text-foreground">Total:</span>
                                            <span className="text-foreground">₹{pricing.total.toLocaleString()}</span>
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* Enterprise Contact */}
                            {currentTier.name === 'Enterprise' && (
                                <div className="p-4 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg">
                                    <h4 className="font-medium text-purple-900 dark:text-purple-100 mb-2">Enterprise Pricing</h4>
                                    <p className="text-sm text-purple-700 dark:text-purple-300 mb-3">
                                        For orders over 10,000 credits, we offer custom pricing and dedicated support.
                                    </p>
                                    <a
                                        href="mailto:<EMAIL>?subject=Enterprise%20Credit%20Pricing%20Inquiry"
                                        className="w-full px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors inline-block text-center"
                                    >
                                        Contact Us
                                    </a>
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                {/* Footer */}
                <div className="flex gap-3 p-6 border-t border-border">
                    <button
                        onClick={onClose}
                        className="flex-1 px-4 py-2 border border-border rounded-lg text-foreground hover:bg-muted transition-colors"
                        disabled={purchasing}
                    >
                        Cancel
                    </button>
                    <button
                        onClick={currentTier.name === 'Enterprise' ?
                            () => window.open('mailto:<EMAIL>?subject=Enterprise%20Credit%20Pricing%20Inquiry', '_blank') :
                            handlePurchase
                        }
                        disabled={selectedCredits < 500 || purchasing}
                        className="flex-1 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                    >
                        {purchasing ? (
                            <>
                                <ArrowPathIcon className="w-4 h-4 animate-spin" />
                                Processing...
                            </>
                        ) : currentTier.name === 'Enterprise' ? (
                            'Contact Us'
                        ) : (
                            `Purchase ₹${pricing.total.toLocaleString()}`
                        )}
                    </button>
                </div>
            </div>
        </div>,
        document.body
    );
};

export default CreditPurchaseModal;
